/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        f1: {
          red: '#FF1E00',
          black: '#15151E',
          white: '#FFFFFF',
          gray: {
            100: '#F8F8F8',
            200: '#E8E8E8',
            300: '#D1D1D1',
            400: '#B4B4B4',
            500: '#9B9B9B',
            600: '#6B6B6B',
            700: '#4A4A4A',
            800: '#2D2D2D',
            900: '#1A1A1A',
          }
        }
      },
      fontFamily: {
        'f1': ['Formula1', 'Arial', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
}
