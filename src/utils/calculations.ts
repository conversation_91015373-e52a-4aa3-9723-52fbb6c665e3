import { 
  DriverMetrics, 
  MetricWeights, 
  SeasonalWeightsByMetric, 
  SeasonalMetricData,
  DriverPerformanceData 
} from '../types/rating';
import { 
  SEASONS, 
  RATING_BOUNDS, 
  PERFORMANCE_THRESHOLDS 
} from '../config/constants';

/**
 * Calculate experience rating based on career total races
 */
export function calculateExperienceRating(totalRaces: number, maxRaces: number = 400): number {
  const normalized = Math.min(totalRaces / maxRaces, 1);
  return Math.round(normalized * RATING_BOUNDS.MAX);
}

/**
 * Calculate racecraft rating based on grid-to-finish position improvements
 */
export function calculateRacecraftRating(
  seasonalData: SeasonalMetricData,
  seasonalWeights: SeasonalWeightsByMetric['RAC']
): number {
  let weightedSum = 0;
  let totalWeight = 0;

  SEASONS.forEach(season => {
    const improvement = seasonalData[season];
    const weight = seasonalWeights[season];

    if (improvement !== undefined && weight > 0) {
      // Convert position improvement to rating (0-100)
      let rating = 50; // Base rating
      
      if (improvement >= PERFORMANCE_THRESHOLDS.EXCELLENT_GRID_IMPROVEMENT) {
        rating = 90 + Math.min(improvement - PERFORMANCE_THRESHOLDS.EXCELLENT_GRID_IMPROVEMENT, 10);
      } else if (improvement >= PERFORMANCE_THRESHOLDS.GOOD_GRID_IMPROVEMENT) {
        rating = 70 + (improvement - PERFORMANCE_THRESHOLDS.GOOD_GRID_IMPROVEMENT) * 10;
      } else if (improvement >= 0) {
        rating = 50 + improvement * 6.67; // Scale 0-3 to 50-70
      } else if (improvement >= PERFORMANCE_THRESHOLDS.POOR_GRID_IMPROVEMENT) {
        rating = 30 + (improvement - PERFORMANCE_THRESHOLDS.POOR_GRID_IMPROVEMENT) * 10;
      } else {
        rating = Math.max(10, 30 + improvement * 5);
      }

      weightedSum += rating * (weight / 100);
      totalWeight += weight / 100;
    }
  });

  return totalWeight > 0 ? Math.round(weightedSum / totalWeight) : 50;
}

/**
 * Calculate awareness rating based on penalty points (inverted)
 */
export function calculateAwarenessRating(
  seasonalData: SeasonalMetricData,
  seasonalWeights: SeasonalWeightsByMetric['AWA']
): number {
  let weightedSum = 0;
  let totalWeight = 0;

  SEASONS.forEach(season => {
    const penaltyPoints = seasonalData[season];
    const weight = seasonalWeights[season];

    if (penaltyPoints !== undefined && weight > 0) {
      // Convert penalty points to rating (0-100, inverted)
      let rating = 100; // Perfect score for no penalties
      
      if (penaltyPoints <= PERFORMANCE_THRESHOLDS.LOW_PENALTY_THRESHOLD) {
        rating = 90 + (PERFORMANCE_THRESHOLDS.LOW_PENALTY_THRESHOLD - penaltyPoints) * 5;
      } else if (penaltyPoints <= PERFORMANCE_THRESHOLDS.HIGH_PENALTY_THRESHOLD) {
        const range = PERFORMANCE_THRESHOLDS.HIGH_PENALTY_THRESHOLD - PERFORMANCE_THRESHOLDS.LOW_PENALTY_THRESHOLD;
        const position = penaltyPoints - PERFORMANCE_THRESHOLDS.LOW_PENALTY_THRESHOLD;
        rating = 90 - (position / range) * 60; // Scale to 30-90
      } else {
        rating = Math.max(10, 30 - (penaltyPoints - PERFORMANCE_THRESHOLDS.HIGH_PENALTY_THRESHOLD) * 2);
      }

      weightedSum += rating * (weight / 100);
      totalWeight += weight / 100;
    }
  });

  return totalWeight > 0 ? Math.round(weightedSum / totalWeight) : 75; // Default good rating
}

/**
 * Calculate pace rating based on lap time performance
 */
export function calculatePaceRating(
  seasonalData: SeasonalMetricData,
  seasonalWeights: SeasonalWeightsByMetric['PAC']
): number {
  let weightedSum = 0;
  let totalWeight = 0;

  SEASONS.forEach(season => {
    const pacePercentage = seasonalData[season]; // % difference from average
    const weight = seasonalWeights[season];

    if (pacePercentage !== undefined && weight > 0) {
      // Convert pace percentage to rating (0-100)
      let rating = 50; // Average rating
      
      if (pacePercentage >= PERFORMANCE_THRESHOLDS.EXCELLENT_PACE_THRESHOLD) {
        rating = 90 + Math.min((pacePercentage - PERFORMANCE_THRESHOLDS.EXCELLENT_PACE_THRESHOLD) * 100, 10);
      } else if (pacePercentage >= PERFORMANCE_THRESHOLDS.GOOD_PACE_THRESHOLD) {
        const range = PERFORMANCE_THRESHOLDS.EXCELLENT_PACE_THRESHOLD - PERFORMANCE_THRESHOLDS.GOOD_PACE_THRESHOLD;
        const position = pacePercentage - PERFORMANCE_THRESHOLDS.GOOD_PACE_THRESHOLD;
        rating = 70 + (position / range) * 20; // Scale to 70-90
      } else if (pacePercentage >= 0) {
        rating = 50 + (pacePercentage / PERFORMANCE_THRESHOLDS.GOOD_PACE_THRESHOLD) * 20; // Scale to 50-70
      } else if (pacePercentage >= PERFORMANCE_THRESHOLDS.POOR_PACE_THRESHOLD) {
        rating = 30 + ((pacePercentage - PERFORMANCE_THRESHOLDS.POOR_PACE_THRESHOLD) / 
                      (0 - PERFORMANCE_THRESHOLDS.POOR_PACE_THRESHOLD)) * 20; // Scale to 30-50
      } else {
        rating = Math.max(10, 30 + pacePercentage * 100);
      }

      weightedSum += rating * (weight / 100);
      totalWeight += weight / 100;
    }
  });

  return totalWeight > 0 ? Math.round(weightedSum / totalWeight) : 50;
}

/**
 * Calculate overall rating from individual metric ratings
 */
export function calculateOverallRating(
  experienceRating: number,
  racecraftRating: number,
  awarenessRating: number,
  paceRating: number,
  weights: MetricWeights
): number {
  const totalWeight = weights.EXP + weights.RAC + weights.AWA + weights.PAC;
  
  if (totalWeight === 0) return 0;

  const weightedSum = 
    (experienceRating * weights.EXP) +
    (racecraftRating * weights.RAC) +
    (awarenessRating * weights.AWA) +
    (paceRating * weights.PAC);

  return Math.round(weightedSum / totalWeight);
}

/**
 * Calculate all ratings for a driver
 */
export function calculateDriverRatings(
  driver: Omit<DriverMetrics, 'experienceRating' | 'racecraftRating' | 'awarenessRating' | 'paceRating' | 'overallRating'>,
  metricWeights: MetricWeights,
  seasonalWeights: SeasonalWeightsByMetric
): DriverMetrics {
  const experienceRating = calculateExperienceRating(driver.experience);
  const racecraftRating = calculateRacecraftRating(driver.racecraft, seasonalWeights.RAC);
  const awarenessRating = calculateAwarenessRating(driver.awareness, seasonalWeights.AWA);
  const paceRating = calculatePaceRating(driver.pace, seasonalWeights.PAC);
  const overallRating = calculateOverallRating(
    experienceRating,
    racecraftRating,
    awarenessRating,
    paceRating,
    metricWeights
  );

  return {
    ...driver,
    experienceRating,
    racecraftRating,
    awarenessRating,
    paceRating,
    overallRating,
  };
}

/**
 * Process raw performance data into metrics
 */
export function processPerformanceData(performanceData: DriverPerformanceData[]): {
  experience: number;
  racecraft: SeasonalMetricData;
  awareness: SeasonalMetricData;
  pace: SeasonalMetricData;
} {
  const racecraft: SeasonalMetricData = {};
  const awareness: SeasonalMetricData = {};
  const pace: SeasonalMetricData = {};
  
  let totalRaces = 0;

  // Group data by season
  const seasonData: { [season: number]: DriverPerformanceData[] } = {};
  performanceData.forEach(data => {
    if (!seasonData[data.season]) {
      seasonData[data.season] = [];
    }
    seasonData[data.season].push(data);
    totalRaces += data.races.length;
  });

  // Calculate metrics for each season
  Object.entries(seasonData).forEach(([seasonStr, data]) => {
    const season = parseInt(seasonStr) as keyof SeasonalMetricData;
    
    // Racecraft: average grid-to-finish improvement
    const improvements = data.flatMap(d => 
      d.races.map(race => race.gridPosition - race.finishPosition)
    ).filter(improvement => !isNaN(improvement));
    
    if (improvements.length > 0) {
      racecraft[season] = improvements.reduce((sum, imp) => sum + imp, 0) / improvements.length;
    }

    // Awareness: total penalty points (to be inverted)
    const penaltyPoints = data.flatMap(d => d.penalties).reduce((sum, penalty) => sum + penalty.penaltyPoints, 0);
    awareness[season] = penaltyPoints;

    // Pace: average lap time performance (placeholder - needs more complex calculation)
    // This would require comparing to session averages and teammate performance
    pace[season] = 0; // Placeholder
  });

  return {
    experience: totalRaces,
    racecraft,
    awareness,
    pace,
  };
}
