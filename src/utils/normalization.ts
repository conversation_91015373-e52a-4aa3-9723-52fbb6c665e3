import { MetricWeights, SeasonalWeights } from '../types/rating';

/**
 * Normalize metric weights to sum to 100%
 */
export function normalizeMetricWeights(weights: MetricWeights): MetricWeights {
  const total = weights.EXP + weights.RAC + weights.AWA + weights.PAC;
  
  if (total === 0) {
    // If all weights are 0, distribute equally
    return {
      EXP: 25,
      RAC: 25,
      AWA: 25,
      PAC: 25,
    };
  }

  const factor = 100 / total;
  
  return {
    EXP: Math.round(weights.EXP * factor),
    RAC: Math.round(weights.RAC * factor),
    AWA: Math.round(weights.AWA * factor),
    PAC: Math.round(weights.PAC * factor),
  };
}

/**
 * Normalize seasonal weights to sum to 100%
 */
export function normalizeSeasonalWeights(weights: SeasonalWeights): SeasonalWeights {
  const total = weights[2021] + weights[2022] + weights[2023] + weights[2024] + weights[2025];
  
  if (total === 0) {
    // If all weights are 0, distribute equally
    return {
      2021: 20,
      2022: 20,
      2023: 20,
      2024: 20,
      2025: 20,
    };
  }

  const factor = 100 / total;
  
  return {
    2021: Math.round(weights[2021] * factor),
    2022: Math.round(weights[2022] * factor),
    2023: Math.round(weights[2023] * factor),
    2024: Math.round(weights[2024] * factor),
    2025: Math.round(weights[2025] * factor),
  };
}

/**
 * Adjust weights when one weight changes to maintain 100% total
 */
export function adjustMetricWeights(
  currentWeights: MetricWeights,
  changedMetric: keyof MetricWeights,
  newValue: number
): MetricWeights {
  const maxValue = 100;
  const clampedValue = Math.max(0, Math.min(maxValue, newValue));
  
  // Calculate remaining weight to distribute
  const otherMetrics = Object.keys(currentWeights).filter(key => key !== changedMetric) as (keyof MetricWeights)[];
  const remainingWeight = maxValue - clampedValue;
  
  if (remainingWeight < 0) {
    // If new value exceeds 100, set it to 100 and others to 0
    const result = { EXP: 0, RAC: 0, AWA: 0, PAC: 0 };
    result[changedMetric] = maxValue;
    return result;
  }

  // Calculate current total of other metrics
  const otherTotal = otherMetrics.reduce((sum, metric) => sum + currentWeights[metric], 0);
  
  const newWeights = { ...currentWeights };
  newWeights[changedMetric] = clampedValue;

  if (otherTotal === 0) {
    // If other metrics are 0, distribute remaining weight equally
    const equalShare = Math.floor(remainingWeight / otherMetrics.length);
    const remainder = remainingWeight % otherMetrics.length;
    
    otherMetrics.forEach((metric, index) => {
      newWeights[metric] = equalShare + (index < remainder ? 1 : 0);
    });
  } else {
    // Proportionally adjust other metrics
    const factor = remainingWeight / otherTotal;
    
    otherMetrics.forEach(metric => {
      newWeights[metric] = Math.round(currentWeights[metric] * factor);
    });
    
    // Handle rounding errors
    const actualTotal = Object.values(newWeights).reduce((sum, val) => sum + val, 0);
    const difference = maxValue - actualTotal;
    
    if (difference !== 0) {
      // Adjust the largest other metric to compensate for rounding
      const largestOtherMetric = otherMetrics.reduce((max, metric) => 
        newWeights[metric] > newWeights[max] ? metric : max
      );
      newWeights[largestOtherMetric] += difference;
    }
  }

  return newWeights;
}

/**
 * Adjust seasonal weights when one weight changes to maintain 100% total
 */
export function adjustSeasonalWeights(
  currentWeights: SeasonalWeights,
  changedSeason: keyof SeasonalWeights,
  newValue: number
): SeasonalWeights {
  const maxValue = 100;
  const clampedValue = Math.max(0, Math.min(maxValue, newValue));
  
  // Calculate remaining weight to distribute
  const otherSeasons = Object.keys(currentWeights).filter(key => key !== changedSeason.toString()) as unknown as (keyof SeasonalWeights)[];
  const remainingWeight = maxValue - clampedValue;
  
  if (remainingWeight < 0) {
    // If new value exceeds 100, set it to 100 and others to 0
    const result = { 2021: 0, 2022: 0, 2023: 0, 2024: 0, 2025: 0 };
    result[changedSeason] = maxValue;
    return result;
  }

  // Calculate current total of other seasons
  const otherTotal = otherSeasons.reduce((sum, season) => sum + currentWeights[season], 0);
  
  const newWeights = { ...currentWeights };
  newWeights[changedSeason] = clampedValue;

  if (otherTotal === 0) {
    // If other seasons are 0, distribute remaining weight equally
    const equalShare = Math.floor(remainingWeight / otherSeasons.length);
    const remainder = remainingWeight % otherSeasons.length;
    
    otherSeasons.forEach((season, index) => {
      newWeights[season] = equalShare + (index < remainder ? 1 : 0);
    });
  } else {
    // Proportionally adjust other seasons
    const factor = remainingWeight / otherTotal;
    
    otherSeasons.forEach(season => {
      newWeights[season] = Math.round(currentWeights[season] * factor);
    });
    
    // Handle rounding errors
    const actualTotal = Object.values(newWeights).reduce((sum, val) => sum + val, 0);
    const difference = maxValue - actualTotal;
    
    if (difference !== 0) {
      // Adjust the largest other season to compensate for rounding
      const largestOtherSeason = otherSeasons.reduce((max, season) => 
        newWeights[season] > newWeights[max] ? season : max
      );
      newWeights[largestOtherSeason] += difference;
    }
  }

  return newWeights;
}

/**
 * Validate that weights sum to approximately 100% (allowing for small rounding errors)
 */
export function validateWeights(weights: Record<string, number>, tolerance: number = 1): boolean {
  const total = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
  return Math.abs(total - 100) <= tolerance;
}

/**
 * Get the percentage representation of weights
 */
export function getWeightPercentages(weights: Record<string, number>): Record<string, string> {
  const result: Record<string, string> = {};
  Object.entries(weights).forEach(([key, value]) => {
    result[key] = `${value}%`;
  });
  return result;
}
