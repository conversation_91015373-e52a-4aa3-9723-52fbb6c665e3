import { 
  Driver, 
  Meeting, 
  Session, 
  Lap, 
  Position, 
  RaceControl, 
  CarData,
  ApiQueryParams,
  ApiResponse 
} from '../types/api';
import { API_BASE_URL, API_ENDPOINTS } from '../config/constants';

class OpenF1Service {
  private baseUrl = API_BASE_URL;

  private async fetchData<T>(endpoint: string, params?: ApiQueryParams): Promise<T[]> {
    try {
      const url = new URL(`${this.baseUrl}${endpoint}`);
      
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            url.searchParams.append(key, String(value));
          }
        });
      }

      const response = await fetch(url.toString());
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return Array.isArray(data) ? data : [data];
    } catch (error) {
      console.error(`Error fetching data from ${endpoint}:`, error);
      throw error;
    }
  }

  // Driver-related methods
  async getDrivers(params?: ApiQueryParams): Promise<Driver[]> {
    return this.fetchData<Driver>(API_ENDPOINTS.DRIVERS, params);
  }

  async getDriversBySession(sessionKey: number): Promise<Driver[]> {
    return this.getDrivers({ session_key: sessionKey });
  }

  async getDriversByMeeting(meetingKey: number): Promise<Driver[]> {
    return this.getDrivers({ meeting_key: meetingKey });
  }

  // Meeting-related methods
  async getMeetings(params?: ApiQueryParams): Promise<Meeting[]> {
    return this.fetchData<Meeting>(API_ENDPOINTS.MEETINGS, params);
  }

  async getMeetingsByYear(year: number): Promise<Meeting[]> {
    return this.getMeetings({ year });
  }

  async getLatestMeeting(): Promise<Meeting[]> {
    return this.getMeetings({ meeting_key: 'latest' });
  }

  // Session-related methods
  async getSessions(params?: ApiQueryParams): Promise<Session[]> {
    return this.fetchData<Session>(API_ENDPOINTS.SESSIONS, params);
  }

  async getSessionsByMeeting(meetingKey: number): Promise<Session[]> {
    return this.getSessions({ meeting_key: meetingKey });
  }

  async getSessionsByYear(year: number): Promise<Session[]> {
    return this.getSessions({ year });
  }

  async getRaceSessions(year?: number): Promise<Session[]> {
    const params: ApiQueryParams = { session_name: 'Race' };
    if (year) params.year = year;
    return this.getSessions(params);
  }

  async getQualifyingSessions(year?: number): Promise<Session[]> {
    const params: ApiQueryParams = { session_name: 'Qualifying' };
    if (year) params.year = year;
    return this.getSessions(params);
  }

  // Lap-related methods
  async getLaps(params?: ApiQueryParams): Promise<Lap[]> {
    return this.fetchData<Lap>(API_ENDPOINTS.LAPS, params);
  }

  async getLapsBySession(sessionKey: number, driverNumber?: number): Promise<Lap[]> {
    const params: ApiQueryParams = { session_key: sessionKey };
    if (driverNumber) params.driver_number = driverNumber;
    return this.getLaps(params);
  }

  async getFastestLaps(sessionKey: number): Promise<Lap[]> {
    const laps = await this.getLapsBySession(sessionKey);
    const fastestLaps: { [driverNumber: number]: Lap } = {};
    
    laps.forEach(lap => {
      if (!lap.lap_duration || lap.is_pit_out_lap) return;
      
      const current = fastestLaps[lap.driver_number];
      if (!current || lap.lap_duration < current.lap_duration) {
        fastestLaps[lap.driver_number] = lap;
      }
    });
    
    return Object.values(fastestLaps);
  }

  // Position-related methods
  async getPositions(params?: ApiQueryParams): Promise<Position[]> {
    return this.fetchData<Position>(API_ENDPOINTS.POSITION, params);
  }

  async getPositionsBySession(sessionKey: number, driverNumber?: number): Promise<Position[]> {
    const params: ApiQueryParams = { session_key: sessionKey };
    if (driverNumber) params.driver_number = driverNumber;
    return this.getPositions(params);
  }

  async getFinalPositions(sessionKey: number): Promise<Position[]> {
    const positions = await this.getPositionsBySession(sessionKey);
    const finalPositions: { [driverNumber: number]: Position } = {};
    
    positions.forEach(position => {
      const current = finalPositions[position.driver_number];
      if (!current || new Date(position.date) > new Date(current.date)) {
        finalPositions[position.driver_number] = position;
      }
    });
    
    return Object.values(finalPositions);
  }

  // Race control methods (for penalties)
  async getRaceControl(params?: ApiQueryParams): Promise<RaceControl[]> {
    return this.fetchData<RaceControl>(API_ENDPOINTS.RACE_CONTROL, params);
  }

  async getPenalties(sessionKey: number, driverNumber?: number): Promise<RaceControl[]> {
    const params: ApiQueryParams = { 
      session_key: sessionKey,
      category: 'Flag'
    };
    if (driverNumber) params.driver_number = driverNumber;
    
    const raceControl = await this.getRaceControl(params);
    return raceControl.filter(event => 
      event.flag && 
      (event.flag.includes('PENALTY') || event.flag.includes('BLACK'))
    );
  }

  // Car data methods
  async getCarData(params?: ApiQueryParams): Promise<CarData[]> {
    return this.fetchData<CarData>(API_ENDPOINTS.CAR_DATA, params);
  }

  // Utility methods for complex queries
  async getDriverPerformanceData(driverNumber: number, year: number) {
    const meetings = await this.getMeetingsByYear(year);
    const performanceData = [];

    for (const meeting of meetings) {
      const sessions = await this.getSessionsByMeeting(meeting.meeting_key);
      const raceSessions = sessions.filter(s => s.session_name === 'Race');
      const qualifyingSessions = sessions.filter(s => s.session_name === 'Qualifying');

      for (const session of [...raceSessions, ...qualifyingSessions]) {
        const laps = await this.getLapsBySession(session.session_key, driverNumber);
        const positions = await this.getPositionsBySession(session.session_key, driverNumber);
        const penalties = await this.getPenalties(session.session_key, driverNumber);

        performanceData.push({
          meeting,
          session,
          laps,
          positions,
          penalties,
        });
      }
    }

    return performanceData;
  }

  async getAllDriversForYear(year: number): Promise<Driver[]> {
    const meetings = await this.getMeetingsByYear(year);
    const allDrivers: { [driverNumber: number]: Driver } = {};

    for (const meeting of meetings) {
      const drivers = await this.getDriversByMeeting(meeting.meeting_key);
      drivers.forEach(driver => {
        allDrivers[driver.driver_number] = driver;
      });
    }

    return Object.values(allDrivers);
  }
}

export const openF1Service = new OpenF1Service();
