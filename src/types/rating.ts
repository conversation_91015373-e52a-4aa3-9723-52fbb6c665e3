// Rating System Types

export interface MetricWeights {
  EXP: number; // Experience
  RAC: number; // Racecraft
  AWA: number; // Awareness
  PAC: number; // Pace
}

export interface SeasonalWeights {
  2021: number;
  2022: number;
  2023: number;
  2024: number;
  2025: number;
}

export interface SeasonalWeightsByMetric {
  RAC: SeasonalWeights;
  AWA: SeasonalWeights;
  PAC: SeasonalWeights;
}

export interface DriverMetrics {
  driverNumber: number;
  driverName: string;
  teamName: string;
  teamColor: string;
  headshot: string;
  
  // Raw metrics
  experience: number; // Career total races
  racecraft: SeasonalMetricData; // Grid-to-finish improvement by season
  awareness: SeasonalMetricData; // Penalty score by season (inverted)
  pace: SeasonalMetricData; // Lap time performance by season
  
  // Calculated ratings
  experienceRating: number;
  racecraftRating: number;
  awarenessRating: number;
  paceRating: number;
  overallRating: number;
}

export interface SeasonalMetricData {
  2021?: number;
  2022?: number;
  2023?: number;
  2024?: number;
  2025?: number;
}

export interface RatingConfiguration {
  metricWeights: MetricWeights;
  seasonalWeights: SeasonalWeightsByMetric;
  name?: string;
  description?: string;
  createdAt?: string;
}

export interface DriverPerformanceData {
  driverNumber: number;
  season: number;
  
  // Race results data
  races: RaceResult[];
  qualifyingResults: QualifyingResult[];
  penalties: PenaltyData[];
  lapTimes: LapTimeData[];
}

export interface RaceResult {
  meetingKey: number;
  sessionKey: number;
  gridPosition: number;
  finishPosition: number;
  points: number;
  status: string;
  fastestLap?: boolean;
}

export interface QualifyingResult {
  meetingKey: number;
  sessionKey: number;
  position: number;
  q1Time?: number;
  q2Time?: number;
  q3Time?: number;
}

export interface PenaltyData {
  meetingKey: number;
  sessionKey: number;
  penaltyType: string;
  penaltyPoints: number;
  description: string;
}

export interface LapTimeData {
  meetingKey: number;
  sessionKey: number;
  lapNumber: number;
  lapTime: number;
  sector1: number;
  sector2: number;
  sector3: number;
  isPersonalBest: boolean;
  isSessionBest: boolean;
}

export interface ComparisonData {
  drivers: DriverMetrics[];
  selectedMetrics: (keyof Pick<DriverMetrics, 'experienceRating' | 'racecraftRating' | 'awarenessRating' | 'paceRating'>)[];
}

export interface ExportData {
  configuration: RatingConfiguration;
  timestamp: string;
  version: string;
}
