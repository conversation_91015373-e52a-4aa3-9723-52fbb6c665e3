import { useQuery, useQueries } from '@tanstack/react-query';
import { openF1Service } from '../services/openf1';
import { Driver, Meeting, Session } from '../types/api';
import { DriverMetrics, DriverPerformanceData } from '../types/rating';
import { CACHE_DURATION, SEASONS } from '../config/constants';
import { processPerformanceData } from '../utils/calculations';

// Query keys
export const queryKeys = {
  drivers: (params?: any) => ['drivers', params],
  meetings: (year?: number) => ['meetings', year],
  sessions: (meetingKey?: number) => ['sessions', meetingKey],
  driverPerformance: (driverNumber: number, year: number) => ['driverPerformance', driverNumber, year],
  allDriversData: () => ['allDriversData'],
} as const;

// Hook to get all drivers for recent years
export function useDrivers() {
  return useQuery({
    queryKey: queryKeys.drivers(),
    queryFn: async () => {
      const allDrivers: { [driverNumber: number]: Driver } = {};
      
      // Get drivers from recent years to build comprehensive list
      for (const year of [2023, 2024, 2025]) {
        try {
          const yearDrivers = await openF1Service.getAllDriversForYear(year);
          yearDrivers.forEach(driver => {
            allDrivers[driver.driver_number] = driver;
          });
        } catch (error) {
          console.warn(`Failed to fetch drivers for year ${year}:`, error);
        }
      }
      
      return Object.values(allDrivers);
    },
    staleTime: CACHE_DURATION.DRIVERS,
    cacheTime: CACHE_DURATION.DRIVERS,
  });
}

// Hook to get meetings for a specific year
export function useMeetings(year: number) {
  return useQuery({
    queryKey: queryKeys.meetings(year),
    queryFn: () => openF1Service.getMeetingsByYear(year),
    staleTime: CACHE_DURATION.MEETINGS,
    cacheTime: CACHE_DURATION.MEETINGS,
    enabled: !!year,
  });
}

// Hook to get sessions for a meeting
export function useSessions(meetingKey: number) {
  return useQuery({
    queryKey: queryKeys.sessions(meetingKey),
    queryFn: () => openF1Service.getSessionsByMeeting(meetingKey),
    staleTime: CACHE_DURATION.SESSIONS,
    cacheTime: CACHE_DURATION.SESSIONS,
    enabled: !!meetingKey,
  });
}

// Hook to get performance data for a specific driver and year
export function useDriverPerformance(driverNumber: number, year: number) {
  return useQuery({
    queryKey: queryKeys.driverPerformance(driverNumber, year),
    queryFn: async () => {
      try {
        const performanceData = await openF1Service.getDriverPerformanceData(driverNumber, year);
        
        // Transform the data into the format we need
        const transformedData: DriverPerformanceData = {
          driverNumber,
          season: year,
          races: [],
          qualifyingResults: [],
          penalties: [],
          lapTimes: [],
        };

        // Process the raw data
        performanceData.forEach(({ meeting, session, laps, positions, penalties }) => {
          if (session.session_name === 'Race') {
            // Process race results
            const gridPosition = positions.find(p => new Date(p.date) === new Date(session.date_start))?.position || 20;
            const finalPosition = positions[positions.length - 1]?.position || 20;
            
            transformedData.races.push({
              meetingKey: meeting.meeting_key,
              sessionKey: session.session_key,
              gridPosition,
              finishPosition: finalPosition,
              points: 0, // Would need additional data source
              status: 'Finished', // Simplified
              fastestLap: false, // Would need to calculate
            });
          } else if (session.session_name === 'Qualifying') {
            // Process qualifying results
            const finalPosition = positions[positions.length - 1]?.position || 20;
            
            transformedData.qualifyingResults.push({
              meetingKey: meeting.meeting_key,
              sessionKey: session.session_key,
              position: finalPosition,
              // Q1, Q2, Q3 times would need additional processing
            });
          }

          // Process penalties
          penalties.forEach(penalty => {
            transformedData.penalties.push({
              meetingKey: meeting.meeting_key,
              sessionKey: session.session_key,
              penaltyType: penalty.flag || 'Unknown',
              penaltyPoints: 1, // Simplified - would need proper penalty point mapping
              description: penalty.message,
            });
          });

          // Process lap times
          laps.forEach(lap => {
            if (lap.lap_duration && !lap.is_pit_out_lap) {
              transformedData.lapTimes.push({
                meetingKey: meeting.meeting_key,
                sessionKey: session.session_key,
                lapNumber: lap.lap_number,
                lapTime: lap.lap_duration,
                sector1: lap.duration_sector_1,
                sector2: lap.duration_sector_2,
                sector3: lap.duration_sector_3,
                isPersonalBest: false, // Would need to calculate
                isSessionBest: false, // Would need to calculate
              });
            }
          });
        });

        return transformedData;
      } catch (error) {
        console.error(`Failed to fetch performance data for driver ${driverNumber} in ${year}:`, error);
        // Return empty data structure on error
        return {
          driverNumber,
          season: year,
          races: [],
          qualifyingResults: [],
          penalties: [],
          lapTimes: [],
        };
      }
    },
    staleTime: CACHE_DURATION.RACE_DATA,
    cacheTime: CACHE_DURATION.RACE_DATA,
    enabled: !!driverNumber && !!year,
  });
}

// Hook to get comprehensive driver metrics data
export function useAllDriversData() {
  const driversQuery = useDrivers();
  
  // Get performance data for all drivers across all seasons
  const performanceQueries = useQueries({
    queries: driversQuery.data?.flatMap(driver => 
      SEASONS.map(year => ({
        queryKey: queryKeys.driverPerformance(driver.driver_number, year),
        queryFn: () => openF1Service.getDriverPerformanceData(driver.driver_number, year),
        staleTime: CACHE_DURATION.RACE_DATA,
        cacheTime: CACHE_DURATION.RACE_DATA,
        enabled: !!driversQuery.data,
      }))
    ) || [],
  });

  return useQuery({
    queryKey: queryKeys.allDriversData(),
    queryFn: async (): Promise<DriverMetrics[]> => {
      if (!driversQuery.data) return [];

      const driverMetrics: DriverMetrics[] = [];

      for (const driver of driversQuery.data) {
        try {
          // Collect performance data for all seasons
          const allPerformanceData: DriverPerformanceData[] = [];
          
          for (const year of SEASONS) {
            const performanceData = await openF1Service.getDriverPerformanceData(driver.driver_number, year);
            
            // Transform and add to collection
            const transformedData: DriverPerformanceData = {
              driverNumber: driver.driver_number,
              season: year,
              races: [],
              qualifyingResults: [],
              penalties: [],
              lapTimes: [],
            };

            // Simplified transformation (same as above)
            performanceData.forEach(({ meeting, session, positions, penalties }) => {
              if (session.session_name === 'Race') {
                const gridPosition = positions[0]?.position || 20;
                const finalPosition = positions[positions.length - 1]?.position || 20;
                
                transformedData.races.push({
                  meetingKey: meeting.meeting_key,
                  sessionKey: session.session_key,
                  gridPosition,
                  finishPosition: finalPosition,
                  points: 0,
                  status: 'Finished',
                });
              }

              penalties.forEach(penalty => {
                transformedData.penalties.push({
                  meetingKey: meeting.meeting_key,
                  sessionKey: session.session_key,
                  penaltyType: penalty.flag || 'Unknown',
                  penaltyPoints: 1,
                  description: penalty.message,
                });
              });
            });

            allPerformanceData.push(transformedData);
          }

          // Process performance data into metrics
          const metrics = processPerformanceData(allPerformanceData);

          // Create driver metrics object
          const driverMetric: Omit<DriverMetrics, 'experienceRating' | 'racecraftRating' | 'awarenessRating' | 'paceRating' | 'overallRating'> = {
            driverNumber: driver.driver_number,
            driverName: driver.full_name,
            teamName: driver.team_name,
            teamColor: `#${driver.team_colour}`,
            headshot: driver.headshot_url,
            experience: metrics.experience,
            racecraft: metrics.racecraft,
            awareness: metrics.awareness,
            pace: metrics.pace,
          };

          // Note: Ratings will be calculated by the store when weights are applied
          driverMetrics.push({
            ...driverMetric,
            experienceRating: 0,
            racecraftRating: 0,
            awarenessRating: 0,
            paceRating: 0,
            overallRating: 0,
          });
        } catch (error) {
          console.error(`Failed to process data for driver ${driver.full_name}:`, error);
        }
      }

      return driverMetrics;
    },
    enabled: !!driversQuery.data && performanceQueries.every(q => q.isSuccess || q.isError),
    staleTime: CACHE_DURATION.RACE_DATA,
    cacheTime: CACHE_DURATION.RACE_DATA,
  });
}
