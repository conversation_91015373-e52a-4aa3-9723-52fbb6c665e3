import React from 'react';
import { useRatingStore } from '../store/ratingStore';
import { METRICS, SEASONS, SLIDER_CONFIG } from '../config/constants';
import { MetricWeights, SeasonalWeightsByMetric } from '../types/rating';

interface SliderProps {
  label: string;
  value: number;
  onChange: (value: number) => void;
  color?: string;
  description?: string;
}

const Slider: React.FC<SliderProps> = ({ label, value, onChange, color = '#FF1E00', description }) => {
  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <label className="text-sm font-medium text-gray-700">{label}</label>
        <span className="text-sm font-semibold text-gray-900">{value}%</span>
      </div>
      {description && (
        <p className="text-xs text-gray-500">{description}</p>
      )}
      <div className="relative">
        <input
          type="range"
          min={SLIDER_CONFIG.min}
          max={SLIDER_CONFIG.max}
          step={SLIDER_CONFIG.step}
          value={value}
          onChange={(e) => onChange(parseInt(e.target.value))}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-track"
          style={{
            background: `linear-gradient(to right, ${color} 0%, ${color} ${value}%, #e5e7eb ${value}%, #e5e7eb 100%)`,
          }}
        />
        <style jsx>{`
          input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: ${color};
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            border: 2px solid white;
          }
          
          input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: ${color};
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            border: 2px solid white;
          }
        `}</style>
      </div>
    </div>
  );
};

const MetricWeightControls: React.FC = () => {
  const { metricWeights, setMetricWeight } = useRatingStore();

  return (
    <div className="card">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Metric Weights</h3>
      <div className="space-y-6">
        {(Object.keys(METRICS) as Array<keyof MetricWeights>).map((metric) => (
          <Slider
            key={metric}
            label={METRICS[metric].name}
            value={metricWeights[metric]}
            onChange={(value) => setMetricWeight(metric, value)}
            color={METRICS[metric].color}
            description={METRICS[metric].description}
          />
        ))}
      </div>
      <div className="mt-4 p-3 bg-gray-50 rounded-md">
        <p className="text-xs text-gray-600">
          Weights automatically normalize to 100%. Adjust any weight and others will scale proportionally.
        </p>
      </div>
    </div>
  );
};

const SeasonalWeightControls: React.FC = () => {
  const { seasonalWeights, setSeasonalWeight } = useRatingStore();

  const seasonalMetrics = ['RAC', 'AWA', 'PAC'] as const;

  return (
    <div className="card">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Seasonal Weights</h3>
      <p className="text-sm text-gray-600 mb-6">
        Apply seasonal weighting to Racecraft, Awareness, and Pace metrics. Experience uses career-aggregate data only.
      </p>
      
      <div className="space-y-8">
        {seasonalMetrics.map((metric) => (
          <div key={metric} className="space-y-4">
            <h4 className="text-md font-medium text-gray-800 flex items-center">
              <span 
                className="w-3 h-3 rounded-full mr-2" 
                style={{ backgroundColor: METRICS[metric].color }}
              />
              {METRICS[metric].name}
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {SEASONS.map((season) => (
                <Slider
                  key={`${metric}-${season}`}
                  label={season.toString()}
                  value={seasonalWeights[metric][season]}
                  onChange={(value) => setSeasonalWeight(metric, season, value)}
                  color={METRICS[metric].color}
                />
              ))}
            </div>
            
            <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
              Total: {Object.values(seasonalWeights[metric]).reduce((sum, val) => sum + val, 0)}%
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-6 p-3 bg-blue-50 rounded-md">
        <p className="text-xs text-blue-700">
          <strong>Seasonal Weighting:</strong> Each metric's seasonal weights are normalized independently. 
          Higher weights give more importance to recent seasons in the final calculation.
        </p>
      </div>
    </div>
  );
};

const WeightControls: React.FC = () => {
  const { resetToDefaults } = useRatingStore();

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold text-gray-900">Rating Configuration</h2>
        <button
          onClick={resetToDefaults}
          className="btn-secondary text-sm"
        >
          Reset to Defaults
        </button>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <MetricWeightControls />
        <SeasonalWeightControls />
      </div>
    </div>
  );
};

export default WeightControls;
