import React from 'react';
import { RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend } from 'recharts';
import { useRatingStore } from '../store/ratingStore';
import { METRICS, CHART_COLORS } from '../config/constants';

const DriverComparison: React.FC = () => {
  const { drivers, selectedDrivers } = useRatingStore();

  const selectedDriverData = drivers.filter(driver => 
    selectedDrivers.includes(driver.driverNumber)
  );

  if (selectedDriverData.length === 0) {
    return (
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Driver Comparison</h3>
        <div className="text-center py-8">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No drivers selected</h3>
          <p className="mt-1 text-sm text-gray-500">
            Select drivers from the table above to compare their ratings.
          </p>
        </div>
      </div>
    );
  }

  // Prepare data for radar chart
  const radarData = [
    {
      metric: 'Experience',
      ...selectedDriverData.reduce((acc, driver, index) => ({
        ...acc,
        [driver.driverName]: driver.experienceRating,
      }), {}),
    },
    {
      metric: 'Racecraft',
      ...selectedDriverData.reduce((acc, driver, index) => ({
        ...acc,
        [driver.driverName]: driver.racecraftRating,
      }), {}),
    },
    {
      metric: 'Awareness',
      ...selectedDriverData.reduce((acc, driver, index) => ({
        ...acc,
        [driver.driverName]: driver.awarenessRating,
      }), {}),
    },
    {
      metric: 'Pace',
      ...selectedDriverData.reduce((acc, driver, index) => ({
        ...acc,
        [driver.driverName]: driver.paceRating,
      }), {}),
    },
  ];

  // Prepare data for bar chart
  const barData = selectedDriverData.map(driver => ({
    name: driver.driverName.split(' ').pop(), // Last name only for space
    Overall: driver.overallRating,
    Experience: driver.experienceRating,
    Racecraft: driver.racecraftRating,
    Awareness: driver.awarenessRating,
    Pace: driver.paceRating,
  }));

  return (
    <div className="space-y-6">
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Driver Comparison</h3>
        
        {/* Selected drivers summary */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Selected Drivers ({selectedDriverData.length})</h4>
          <div className="flex flex-wrap gap-2">
            {selectedDriverData.map((driver, index) => (
              <div 
                key={driver.driverNumber}
                className="flex items-center space-x-2 bg-gray-100 rounded-full px-3 py-1"
              >
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: CHART_COLORS[index % CHART_COLORS.length] }}
                />
                <span className="text-sm font-medium">{driver.driverName}</span>
                <span className="text-xs text-gray-500">({driver.overallRating})</span>
              </div>
            ))}
          </div>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Radar Chart */}
          <div>
            <h4 className="text-md font-medium text-gray-800 mb-4">Metric Breakdown</h4>
            <ResponsiveContainer width="100%" height={300}>
              <RadarChart data={radarData}>
                <PolarGrid />
                <PolarAngleAxis dataKey="metric" />
                <PolarRadiusAxis 
                  angle={90} 
                  domain={[0, 100]} 
                  tick={{ fontSize: 12 }}
                />
                {selectedDriverData.map((driver, index) => (
                  <Radar
                    key={driver.driverNumber}
                    name={driver.driverName}
                    dataKey={driver.driverName}
                    stroke={CHART_COLORS[index % CHART_COLORS.length]}
                    fill={CHART_COLORS[index % CHART_COLORS.length]}
                    fillOpacity={0.1}
                    strokeWidth={2}
                  />
                ))}
                <Tooltip />
                <Legend />
              </RadarChart>
            </ResponsiveContainer>
          </div>

          {/* Bar Chart */}
          <div>
            <h4 className="text-md font-medium text-gray-800 mb-4">Rating Comparison</h4>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={barData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis domain={[0, 100]} />
                <Tooltip />
                <Legend />
                <Bar dataKey="Overall" fill="#FF1E00" />
                <Bar dataKey="Experience" fill={METRICS.EXP.color} />
                <Bar dataKey="Racecraft" fill={METRICS.RAC.color} />
                <Bar dataKey="Awareness" fill={METRICS.AWA.color} />
                <Bar dataKey="Pace" fill={METRICS.PAC.color} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Detailed comparison table */}
        <div className="mt-6">
          <h4 className="text-md font-medium text-gray-800 mb-4">Detailed Comparison</h4>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Driver</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Overall</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Experience</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Racecraft</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Awareness</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Pace</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {selectedDriverData.map((driver, index) => (
                  <tr key={driver.driverNumber}>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div 
                          className="w-3 h-3 rounded-full mr-2" 
                          style={{ backgroundColor: CHART_COLORS[index % CHART_COLORS.length] }}
                        />
                        <span className="text-sm font-medium text-gray-900">{driver.driverName}</span>
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">
                      {driver.overallRating}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      {driver.experienceRating}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      {driver.racecraftRating}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      {driver.awarenessRating}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      {driver.paceRating}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DriverComparison;
