import React, { useState, useRef } from 'react';
import { useRatingStore } from '../store/ratingStore';
import { RatingConfiguration } from '../types/rating';
import { EXPORT_VERSION, MAX_IMPORT_FILE_SIZE } from '../config/constants';

const ConfigManager: React.FC = () => {
  const { exportConfiguration, loadConfiguration, resetToDefaults } = useRatingStore();
  const [isExporting, setIsExporting] = useState(false);
  const [importError, setImportError] = useState<string | null>(null);
  const [importSuccess, setImportSuccess] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleExportJSON = () => {
    setIsExporting(true);
    try {
      const config = exportConfiguration();
      const dataStr = JSON.stringify(config, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `f1-rating-config-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleExportURL = () => {
    try {
      const config = exportConfiguration();
      const encodedConfig = btoa(JSON.stringify(config));
      const shareableURL = `${window.location.origin}${window.location.pathname}?config=${encodedConfig}`;
      
      navigator.clipboard.writeText(shareableURL).then(() => {
        alert('Shareable URL copied to clipboard!');
      }).catch(() => {
        // Fallback for browsers that don't support clipboard API
        const textArea = document.createElement('textarea');
        textArea.value = shareableURL;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('Shareable URL copied to clipboard!');
      });
    } catch (error) {
      console.error('URL export failed:', error);
      alert('Failed to create shareable URL');
    }
  };

  const handleImportFile = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setImportError(null);
    setImportSuccess(false);

    // Check file size
    if (file.size > MAX_IMPORT_FILE_SIZE) {
      setImportError('File is too large. Maximum size is 1MB.');
      return;
    }

    // Check file type
    if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
      setImportError('Please select a valid JSON file.');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const config: RatingConfiguration = JSON.parse(content);
        
        // Validate configuration structure
        if (!validateConfiguration(config)) {
          setImportError('Invalid configuration file format.');
          return;
        }

        loadConfiguration(config);
        setImportSuccess(true);
        setTimeout(() => setImportSuccess(false), 3000);
      } catch (error) {
        setImportError('Failed to parse JSON file. Please check the file format.');
      }
    };

    reader.onerror = () => {
      setImportError('Failed to read file.');
    };

    reader.readAsText(file);
  };

  const validateConfiguration = (config: any): config is RatingConfiguration => {
    return (
      config &&
      typeof config === 'object' &&
      config.metricWeights &&
      typeof config.metricWeights.EXP === 'number' &&
      typeof config.metricWeights.RAC === 'number' &&
      typeof config.metricWeights.AWA === 'number' &&
      typeof config.metricWeights.PAC === 'number' &&
      config.seasonalWeights &&
      config.seasonalWeights.RAC &&
      config.seasonalWeights.AWA &&
      config.seasonalWeights.PAC
    );
  };

  // Check for URL configuration on component mount
  React.useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const configParam = urlParams.get('config');
    
    if (configParam) {
      try {
        const decodedConfig = JSON.parse(atob(configParam));
        if (validateConfiguration(decodedConfig)) {
          loadConfiguration(decodedConfig);
          // Remove config from URL after loading
          const newUrl = window.location.pathname;
          window.history.replaceState({}, document.title, newUrl);
        }
      } catch (error) {
        console.error('Failed to load configuration from URL:', error);
      }
    }
  }, [loadConfiguration]);

  return (
    <div className="card">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Configuration Management</h3>
      
      <div className="space-y-6">
        {/* Export Section */}
        <div>
          <h4 className="text-md font-medium text-gray-800 mb-3">Export Configuration</h4>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={handleExportJSON}
              disabled={isExporting}
              className="btn-primary"
            >
              {isExporting ? 'Exporting...' : 'Export as JSON'}
            </button>
            <button
              onClick={handleExportURL}
              className="btn-secondary"
            >
              Copy Shareable URL
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Export your current rating configuration to share with others or save for later use.
          </p>
        </div>

        {/* Import Section */}
        <div>
          <h4 className="text-md font-medium text-gray-800 mb-3">Import Configuration</h4>
          <div className="space-y-3">
            <div>
              <input
                ref={fileInputRef}
                type="file"
                accept=".json,application/json"
                onChange={handleImportFile}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-f1-red file:text-white hover:file:bg-red-600"
              />
            </div>
            
            {importError && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-700">{importError}</p>
              </div>
            )}
            
            {importSuccess && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm text-green-700">Configuration imported successfully!</p>
              </div>
            )}
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Import a previously exported configuration file to restore your settings.
          </p>
        </div>

        {/* Reset Section */}
        <div>
          <h4 className="text-md font-medium text-gray-800 mb-3">Reset Configuration</h4>
          <button
            onClick={resetToDefaults}
            className="btn-secondary text-red-600 hover:bg-red-50"
          >
            Reset to Defaults
          </button>
          <p className="text-xs text-gray-500 mt-2">
            Reset all weights and settings to their default values.
          </p>
        </div>

        {/* Information */}
        <div className="bg-blue-50 p-4 rounded-md">
          <h5 className="text-sm font-medium text-blue-800 mb-2">Configuration Details</h5>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• Metric weights control the importance of each rating component</li>
            <li>• Seasonal weights determine how much each year contributes to the final metric</li>
            <li>• All weights are automatically normalized to sum to 100%</li>
            <li>• Configurations can be shared via JSON files or URLs</li>
            <li>• Version: {EXPORT_VERSION}</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ConfigManager;
