import React, { useMemo } from 'react';
import { useRatingStore } from '../store/ratingStore';
import type { DriverMetrics } from '../types/rating';
import { METRICS } from '../config/constants';

interface TableHeaderProps {
  label: string;
  sortKey: keyof DriverMetrics;
  currentSort: keyof DriverMetrics;
  sortOrder: 'asc' | 'desc';
  onSort: (key: keyof DriverMetrics) => void;
  className?: string;
}

const TableHeader: React.FC<TableHeaderProps> = ({
  label,
  sortKey,
  currentSort,
  sortOrder,
  onSort,
  className = ''
}) => {
  const isActive = currentSort === sortKey;

  return (
    <th
      className={`px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-50 ${className}`}
      onClick={() => onSort(sortKey)}
    >
      <div className="flex items-center space-x-1">
        <span>{label}</span>
        <div className="flex flex-col">
          <svg
            className={`w-3 h-3 ${isActive && sortOrder === 'asc' ? 'text-f1-red' : 'text-gray-300'}`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          <svg
            className={`w-3 h-3 ${isActive && sortOrder === 'desc' ? 'text-f1-red' : 'text-gray-300'}`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </div>
      </div>
    </th>
  );
};

interface RatingCellProps {
  value: number;
  color?: string;
}

const RatingCell: React.FC<RatingCellProps> = ({ value, color = '#FF1E00' }) => {
  const getBackgroundColor = (rating: number) => {
    if (rating >= 80) return 'bg-green-100 text-green-800';
    if (rating >= 60) return 'bg-yellow-100 text-yellow-800';
    if (rating >= 40) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  return (
    <td className="px-4 py-4 whitespace-nowrap">
      <div className="flex items-center">
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getBackgroundColor(value)}`}>
          {value}
        </span>
        <div className="ml-2 w-16 bg-gray-200 rounded-full h-2">
          <div
            className="h-2 rounded-full transition-all duration-300"
            style={{
              width: `${value}%`,
              backgroundColor: color
            }}
          />
        </div>
      </div>
    </td>
  );
};

const DriverTable: React.FC = () => {
  const {
    drivers,
    sortBy,
    sortOrder,
    searchTerm,
    selectedDrivers,
    setSortBy,
    setSearchTerm,
    toggleDriverSelection,
    clearDriverSelection
  } = useRatingStore();

  const filteredAndSortedDrivers = useMemo(() => {
    let filtered = drivers;

    // Apply search filter
    if (searchTerm) {
      filtered = drivers.filter(driver =>
        driver.driverName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        driver.teamName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sortBy];
      const bValue = b[sortBy];

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortOrder === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      return 0;
    });

    return filtered;
  }, [drivers, searchTerm, sortBy, sortOrder]);

  const handleSelectAll = () => {
    if (selectedDrivers.length === filteredAndSortedDrivers.length) {
      clearDriverSelection();
    } else {
      clearDriverSelection();
      filteredAndSortedDrivers.forEach(driver => {
        toggleDriverSelection(driver.driverNumber);
      });
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Driver Rankings</h3>
        <div className="flex items-center space-x-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Search drivers or teams..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-red-600 focus:border-red-600"
            />
            <svg
              className="absolute left-3 top-2.5 h-5 w-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          {selectedDrivers.length > 0 && (
            <span className="text-sm text-gray-600">
              {selectedDrivers.length} selected
            </span>
          )}
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3">
                <input
                  type="checkbox"
                  checked={selectedDrivers.length === filteredAndSortedDrivers.length && filteredAndSortedDrivers.length > 0}
                  onChange={handleSelectAll}
                  className="rounded border-gray-300 text-red-600 focus:ring-red-600"
                />
              </th>
              <TableHeader
                label="Driver"
                sortKey="driverName"
                currentSort={sortBy}
                sortOrder={sortOrder}
                onSort={setSortBy}
                className="sticky left-0 bg-gray-50"
              />
              <TableHeader
                label="Team"
                sortKey="teamName"
                currentSort={sortBy}
                sortOrder={sortOrder}
                onSort={setSortBy}
              />
              <TableHeader
                label="Overall"
                sortKey="overallRating"
                currentSort={sortBy}
                sortOrder={sortOrder}
                onSort={setSortBy}
              />
              <TableHeader
                label="Experience"
                sortKey="experienceRating"
                currentSort={sortBy}
                sortOrder={sortOrder}
                onSort={setSortBy}
              />
              <TableHeader
                label="Racecraft"
                sortKey="racecraftRating"
                currentSort={sortBy}
                sortOrder={sortOrder}
                onSort={setSortBy}
              />
              <TableHeader
                label="Awareness"
                sortKey="awarenessRating"
                currentSort={sortBy}
                sortOrder={sortOrder}
                onSort={setSortBy}
              />
              <TableHeader
                label="Pace"
                sortKey="paceRating"
                currentSort={sortBy}
                sortOrder={sortOrder}
                onSort={setSortBy}
              />
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredAndSortedDrivers.map((driver) => (
              <tr
                key={driver.driverNumber}
                className={`hover:bg-gray-50 ${selectedDrivers.includes(driver.driverNumber) ? 'bg-blue-50' : ''}`}
              >
                <td className="px-4 py-4">
                  <input
                    type="checkbox"
                    checked={selectedDrivers.includes(driver.driverNumber)}
                    onChange={() => toggleDriverSelection(driver.driverNumber)}
                    className="rounded border-gray-300 text-red-600 focus:ring-red-600"
                  />
                </td>
                <td className="px-4 py-4 whitespace-nowrap sticky left-0 bg-white">
                  <div className="flex items-center">
                    <img
                      className="h-10 w-10 rounded-full object-cover"
                      src={driver.headshot}
                      alt={driver.driverName}
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = `https://via.placeholder.com/40x40/cccccc/666666?text=${driver.driverName.split(' ').map(n => n[0]).join('')}`;
                      }}
                    />
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{driver.driverName}</div>
                      <div className="text-sm text-gray-500">#{driver.driverNumber}</div>
                    </div>
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div
                      className="w-4 h-4 rounded-full mr-2"
                      style={{ backgroundColor: driver.teamColor }}
                    />
                    <span className="text-sm text-gray-900">{driver.teamName}</span>
                  </div>
                </td>
                <RatingCell value={driver.overallRating} color="#FF1E00" />
                <RatingCell value={driver.experienceRating} color={METRICS.EXP.color} />
                <RatingCell value={driver.racecraftRating} color={METRICS.RAC.color} />
                <RatingCell value={driver.awarenessRating} color={METRICS.AWA.color} />
                <RatingCell value={driver.paceRating} color={METRICS.PAC.color} />
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredAndSortedDrivers.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">No drivers found matching your search criteria.</p>
        </div>
      )}
    </div>
  );
};

export default DriverTable;
