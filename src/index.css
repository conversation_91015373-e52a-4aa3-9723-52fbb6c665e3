@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-white text-gray-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .slider-thumb {
    @apply appearance-none w-5 h-5 bg-f1-red rounded-full cursor-pointer shadow-lg;
    @apply hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-f1-red focus:ring-opacity-50;
  }

  .slider-track {
    @apply appearance-none h-2 bg-gray-200 rounded-lg outline-none;
  }

  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200 p-6;
  }

  .btn-primary {
    @apply bg-f1-red text-white px-4 py-2 rounded-md font-medium;
    @apply hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-f1-red focus:ring-opacity-50;
    @apply transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-100 text-gray-700 px-4 py-2 rounded-md font-medium;
    @apply hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-opacity-50;
    @apply transition-colors duration-200;
  }
}
