import { MetricWeights, SeasonalWeightsByMetric } from '../types/rating';

// API Configuration
export const API_BASE_URL = 'https://api.openf1.org/v1';

export const API_ENDPOINTS = {
  DRIVERS: '/drivers',
  MEETINGS: '/meetings',
  SESSIONS: '/sessions',
  LAPS: '/laps',
  POSITION: '/position',
  RACE_CONTROL: '/race_control',
  CAR_DATA: '/car_data',
} as const;

// Default Rating Configuration
export const DEFAULT_METRIC_WEIGHTS: MetricWeights = {
  EXP: 25, // 25%
  RAC: 25, // 25%
  AWA: 25, // 25%
  PAC: 25, // 25%
};

export const DEFAULT_SEASONAL_WEIGHTS: SeasonalWeightsByMetric = {
  RAC: {
    2021: 10, // 10%
    2022: 15, // 15%
    2023: 25, // 25%
    2024: 30, // 30%
    2025: 20, // 20%
  },
  AWA: {
    2021: 10, // 10%
    2022: 15, // 15%
    2023: 25, // 25%
    2024: 30, // 30%
    2025: 20, // 20%
  },
  PAC: {
    2021: 10, // 10%
    2022: 15, // 15%
    2023: 25, // 25%
    2024: 30, // 30%
    2025: 20, // 20%
  },
};

// Seasons for seasonal weighting
export const SEASONS = [2021, 2022, 2023, 2024, 2025] as const;

// Metric definitions
export const METRICS = {
  EXP: {
    name: 'Experience',
    description: 'Career total races, normalized 0-100',
    color: '#3B82F6', // Blue
    seasonal: false,
  },
  RAC: {
    name: 'Racecraft',
    description: 'Average grid-to-finish position improvement per season',
    color: '#10B981', // Green
    seasonal: true,
  },
  AWA: {
    name: 'Awareness',
    description: 'Inverse penalty score per season (fewer penalties = higher score)',
    color: '#F59E0B', // Yellow
    seasonal: true,
  },
  PAC: {
    name: 'Pace',
    description: 'Lap time performance vs session fastest and teammate',
    color: '#EF4444', // Red
    seasonal: true,
  },
} as const;

// Cache configuration
export const CACHE_DURATION = {
  DRIVERS: 1000 * 60 * 60, // 1 hour
  MEETINGS: 1000 * 60 * 60 * 24, // 24 hours
  SESSIONS: 1000 * 60 * 60, // 1 hour
  RACE_DATA: 1000 * 60 * 30, // 30 minutes
} as const;

// UI Configuration
export const SLIDER_CONFIG = {
  min: 0,
  max: 100,
  step: 1,
} as const;

export const CHART_COLORS = [
  '#FF1E00', // F1 Red
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // Yellow
  '#8B5CF6', // Purple
  '#F97316', // Orange
  '#06B6D4', // Cyan
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6B7280', // Gray
] as const;

// Table configuration
export const TABLE_PAGE_SIZES = [10, 20, 50, 100] as const;
export const DEFAULT_PAGE_SIZE = 20;

// Export/Import configuration
export const EXPORT_VERSION = '1.0.0';
export const MAX_IMPORT_FILE_SIZE = 1024 * 1024; // 1MB

// Driver number ranges (for validation)
export const VALID_DRIVER_NUMBERS = {
  MIN: 1,
  MAX: 99,
} as const;

// Rating calculation constants
export const RATING_BOUNDS = {
  MIN: 0,
  MAX: 100,
} as const;

// Performance thresholds for calculations
export const PERFORMANCE_THRESHOLDS = {
  EXCELLENT_GRID_IMPROVEMENT: 5, // positions gained
  GOOD_GRID_IMPROVEMENT: 3,
  POOR_GRID_IMPROVEMENT: -2, // positions lost
  
  LOW_PENALTY_THRESHOLD: 2, // penalty points per season
  HIGH_PENALTY_THRESHOLD: 10,
  
  EXCELLENT_PACE_THRESHOLD: 0.2, // % faster than average
  GOOD_PACE_THRESHOLD: 0.1,
  POOR_PACE_THRESHOLD: -0.2, // % slower than average
} as const;
