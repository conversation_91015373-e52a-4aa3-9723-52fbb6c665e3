import React, { useEffect, useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useRatingStore } from './store/ratingStore';
import { useAllDriversData } from './hooks/useF1Data';
import WeightControls from './components/WeightControls';
import DriverTable from './components/DriverTable';
import DriverComparison from './components/DriverComparison';
import ConfigManager from './components/ConfigManager';
import LoadingSpinner from './components/LoadingSpinner';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      refetchOnWindowFocus: false,
    },
  },
});

const AppContent: React.FC = () => {
  const { setDrivers, recalculateRatings, selectedDrivers } = useRatingStore();
  const { data: driversData, isLoading, error } = useAllDriversData();
  const [activeTab, setActiveTab] = useState<'ratings' | 'comparison' | 'config'>('ratings');

  useEffect(() => {
    if (driversData) {
      setDrivers(driversData);
      recalculateRatings();
    }
  }, [driversData, setDrivers, recalculateRatings]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading F1 driver data..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <svg className="mx-auto h-12 w-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading data</h3>
          <p className="mt-1 text-sm text-gray-500">
            Failed to load F1 driver data. Please check your internet connection and try again.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 bg-red-600 text-white px-4 py-2 rounded-md font-medium hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-gray-900">
                  F1 Driver Rating Lab
                </h1>
              </div>
              <div className="ml-4">
                <p className="text-sm text-gray-500">
                  Create custom Formula 1 driver ratings through dynamic weighting controls
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-500">
                {driversData?.length || 0} drivers loaded
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            <button
              onClick={() => setActiveTab('ratings')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'ratings'
                  ? 'border-red-600 text-red-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Driver Ratings
            </button>
            <button
              onClick={() => setActiveTab('comparison')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'comparison'
                  ? 'border-red-600 text-red-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Driver Comparison
              {selectedDrivers.length > 0 && (
                <span className="ml-2 bg-red-600 text-white text-xs rounded-full px-2 py-1">
                  {selectedDrivers.length}
                </span>
              )}
            </button>
            <button
              onClick={() => setActiveTab('config')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'config'
                  ? 'border-red-600 text-red-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Configuration
            </button>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Weight Controls - Always visible */}
          <WeightControls />

          {/* Tab Content */}
          {activeTab === 'ratings' && <DriverTable />}
          {activeTab === 'comparison' && <DriverComparison />}
          {activeTab === 'config' && <ConfigManager />}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-sm text-gray-500">
            <p>
              Data provided by{' '}
              <a
                href="https://openf1.org"
                target="_blank"
                rel="noopener noreferrer"
                className="text-red-600 hover:underline"
              >
                OpenF1.org
              </a>
              {' '}• Built with React, TypeScript, and Tailwind CSS
            </p>
            <p className="mt-2">
              This is an unofficial project and is not associated with Formula 1.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AppContent />
    </QueryClientProvider>
  );
}

export default App;
