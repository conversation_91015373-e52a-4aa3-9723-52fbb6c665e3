import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { 
  MetricWeights, 
  SeasonalWeightsByMetric, 
  DriverMetrics, 
  RatingConfiguration,
  ComparisonData 
} from '../types/rating';
import { 
  DEFAULT_METRIC_WEIGHTS, 
  DEFAULT_SEASONAL_WEIGHTS 
} from '../config/constants';
import { 
  normalizeMetricWeights, 
  normalizeSeasonalWeights,
  adjustMetricWeights,
  adjustSeasonalWeights 
} from '../utils/normalization';
import { calculateDriverRatings } from '../utils/calculations';

interface RatingState {
  // Configuration
  metricWeights: MetricWeights;
  seasonalWeights: SeasonalWeightsByMetric;
  
  // Data
  drivers: DriverMetrics[];
  isLoading: boolean;
  error: string | null;
  
  // UI State
  selectedDrivers: number[];
  sortBy: keyof DriverMetrics;
  sortOrder: 'asc' | 'desc';
  searchTerm: string;
  
  // Comparison
  comparisonData: ComparisonData | null;
  
  // Actions
  setMetricWeight: (metric: keyof MetricWeights, value: number) => void;
  setSeasonalWeight: (metric: keyof SeasonalWeightsByMetric, season: keyof SeasonalWeightsByMetric['RAC'], value: number) => void;
  setDrivers: (drivers: DriverMetrics[]) => void;
  recalculateRatings: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setSortBy: (sortBy: keyof DriverMetrics, order?: 'asc' | 'desc') => void;
  setSearchTerm: (term: string) => void;
  toggleDriverSelection: (driverNumber: number) => void;
  clearDriverSelection: () => void;
  setComparisonData: (data: ComparisonData | null) => void;
  resetToDefaults: () => void;
  loadConfiguration: (config: RatingConfiguration) => void;
  exportConfiguration: () => RatingConfiguration;
}

export const useRatingStore = create<RatingState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        metricWeights: DEFAULT_METRIC_WEIGHTS,
        seasonalWeights: DEFAULT_SEASONAL_WEIGHTS,
        drivers: [],
        isLoading: false,
        error: null,
        selectedDrivers: [],
        sortBy: 'overallRating',
        sortOrder: 'desc',
        searchTerm: '',
        comparisonData: null,

        // Actions
        setMetricWeight: (metric, value) => {
          const currentWeights = get().metricWeights;
          const newWeights = adjustMetricWeights(currentWeights, metric, value);
          
          set({ metricWeights: newWeights });
          get().recalculateRatings();
        },

        setSeasonalWeight: (metric, season, value) => {
          const currentWeights = get().seasonalWeights;
          const currentSeasonalWeights = currentWeights[metric];
          const newSeasonalWeights = adjustSeasonalWeights(currentSeasonalWeights, season, value);
          
          set({
            seasonalWeights: {
              ...currentWeights,
              [metric]: newSeasonalWeights,
            },
          });
          get().recalculateRatings();
        },

        setDrivers: (drivers) => {
          set({ drivers });
        },

        recalculateRatings: () => {
          const { drivers, metricWeights, seasonalWeights } = get();
          
          const updatedDrivers = drivers.map(driver => {
            const driverWithoutRatings = {
              driverNumber: driver.driverNumber,
              driverName: driver.driverName,
              teamName: driver.teamName,
              teamColor: driver.teamColor,
              headshot: driver.headshot,
              experience: driver.experience,
              racecraft: driver.racecraft,
              awareness: driver.awareness,
              pace: driver.pace,
            };
            
            return calculateDriverRatings(driverWithoutRatings, metricWeights, seasonalWeights);
          });

          set({ drivers: updatedDrivers });
        },

        setLoading: (loading) => {
          set({ isLoading: loading });
        },

        setError: (error) => {
          set({ error });
        },

        setSortBy: (sortBy, order) => {
          const currentOrder = get().sortOrder;
          const newOrder = order || (get().sortBy === sortBy ? (currentOrder === 'asc' ? 'desc' : 'asc') : 'desc');
          
          set({ sortBy, sortOrder: newOrder });
        },

        setSearchTerm: (term) => {
          set({ searchTerm: term });
        },

        toggleDriverSelection: (driverNumber) => {
          const selectedDrivers = get().selectedDrivers;
          const isSelected = selectedDrivers.includes(driverNumber);
          
          if (isSelected) {
            set({ selectedDrivers: selectedDrivers.filter(num => num !== driverNumber) });
          } else {
            set({ selectedDrivers: [...selectedDrivers, driverNumber] });
          }
        },

        clearDriverSelection: () => {
          set({ selectedDrivers: [] });
        },

        setComparisonData: (data) => {
          set({ comparisonData: data });
        },

        resetToDefaults: () => {
          set({
            metricWeights: DEFAULT_METRIC_WEIGHTS,
            seasonalWeights: DEFAULT_SEASONAL_WEIGHTS,
            selectedDrivers: [],
            sortBy: 'overallRating',
            sortOrder: 'desc',
            searchTerm: '',
            comparisonData: null,
          });
          get().recalculateRatings();
        },

        loadConfiguration: (config) => {
          set({
            metricWeights: normalizeMetricWeights(config.metricWeights),
            seasonalWeights: {
              RAC: normalizeSeasonalWeights(config.seasonalWeights.RAC),
              AWA: normalizeSeasonalWeights(config.seasonalWeights.AWA),
              PAC: normalizeSeasonalWeights(config.seasonalWeights.PAC),
            },
          });
          get().recalculateRatings();
        },

        exportConfiguration: () => {
          const { metricWeights, seasonalWeights } = get();
          return {
            metricWeights,
            seasonalWeights,
            name: 'Custom Configuration',
            description: 'Exported configuration',
            createdAt: new Date().toISOString(),
          };
        },
      }),
      {
        name: 'f1-rating-store',
        partialize: (state) => ({
          metricWeights: state.metricWeights,
          seasonalWeights: state.seasonalWeights,
          selectedDrivers: state.selectedDrivers,
          sortBy: state.sortBy,
          sortOrder: state.sortOrder,
        }),
      }
    ),
    {
      name: 'F1 Rating Store',
    }
  )
);
